package org.jeecg.modules.cw.statistics.controller;

import java.util.*;
import org.jeecg.common.api.vo.Result;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.modules.cw.statistics.entity.CwStatistics;
import org.jeecg.modules.cw.statistics.result.*;
import org.jeecg.modules.cw.statistics.service.ICwStatisticsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import javax.annotation.Resource;
import java.util.List;
import org.jeecg.modules.cw.statistics.result.CwProfitTrendPointResult;
import org.jeecg.modules.cw.statistics.result.CwMetalProfitBarResult;

@Api(tags="统计")
@RestController
@RequestMapping("/statistics")
@Slf4j
public class CwStatisticsController extends JeecgController<CwStatistics, ICwStatisticsService> {

	@Resource
	private ICwStatisticsService cwStatisticsService;

	/**
	 * 金属价格（月）
	 * @param date 当天日期（yyyy-MM-dd），为空默认为今天
	 * @return Result<List<CwMetalPriceResult>>
	 */
	@ApiOperation(value = "金属价格（月）", notes = "传入当天时间，返回当月1号至今的金属价格数据")
	@GetMapping("/metalPriceMonth")
	public Result<List<CwMetalPriceResult>> metalPriceMonth(
			@RequestParam(value = "date", required = false)
			@DateTimeFormat(pattern = "yyyy-MM-dd") Date date) {
		List<CwMetalPriceResult> data = cwStatisticsService.getMetalPriceMonth(date);
		return Result.OK(data);
	}

	/**
	 * 利润统计（按天/月/年）
	 * @param date 基准日期（yyyy-MM-dd），为空默认今天
	 * @param dimension 统计维度：day | month | year
	 * @return Result<CwProfitStatResult>
	 */
	@ApiOperation(value = "利润统计", notes = "统计指定维度（日/月/年）的利润及环比、同比、计划比。年累计无环比")
	@GetMapping("/profitStatistics")
	public Result<CwProfitStatResult> profitStatistics(
			@RequestParam(value = "date", required = false)
			@DateTimeFormat(pattern = "yyyy-MM-dd") Date date,
			@RequestParam(value = "dimension", required = false, defaultValue = "day") String dimension) {
		CwProfitStatResult data = cwStatisticsService.profitStatistics(date, dimension);
		return Result.OK(data);
	}

	/**
	 * 利润趋势数据（日7天 / 月全年）
	 */
	@ApiOperation(value = "利润趋势", notes = "dimension=day 返回最近7天，dimension=month 返回当年1月到当前月")
	@GetMapping("/profitTrend")
	public Result<List<CwProfitTrendPointResult>> profitTrend(
			@RequestParam(value = "date", required = false)
			@DateTimeFormat(pattern = "yyyy-MM-dd") Date date,
			@RequestParam(value = "dimension", required = false, defaultValue = "day") String dimension) {
		List<CwProfitTrendPointResult> data = cwStatisticsService.profitTrend(date, dimension);
		return Result.OK(data);
	}

	/**
	 * 金属价量分析利润柱状图
	 * @param date 基准日期（yyyy-MM-dd），为空默认今天
	 * @param dimension 维度：month | year
	 */
	@ApiOperation(value = "金属利润柱状图", notes = "返回各金属（月/年）计划值与实际值利润数据")
	@GetMapping("/metalProfitBar")
	public Result<List<CwMetalProfitBarResult>> metalProfitBar(
			@RequestParam(value = "date", required = false)
			@DateTimeFormat(pattern = "yyyy-MM-dd") Date date,
			@RequestParam(value = "dimension", required = false, defaultValue = "month") String dimension) {
		List<CwMetalProfitBarResult> data = cwStatisticsService.metalProfitBar(date, dimension);
		return Result.OK(data);
	}

	/**
	 * 成本统计（总成本、吨矿成本、金属成本）
	 * @param date 基准日期（yyyy-MM-dd），为空默认今天
	 * @param dimension 维度：month | year
	 */
	@ApiOperation(value = "成本统计", notes = "返回指定月份或年份的总成本、吨矿成本、金属成本")
	@GetMapping("/costStatistics")
	public Result<org.jeecg.modules.cw.statistics.result.CwCostStatResult> costStatistics(
			@RequestParam(value = "date", required = false)
			@DateTimeFormat(pattern = "yyyy-MM-dd") Date date,
			@RequestParam(value = "dimension", required = false, defaultValue = "month") String dimension) {
		org.jeecg.modules.cw.statistics.result.CwCostStatResult data = cwStatisticsService.costStatistics(date, dimension);
		return Result.OK(data);
	}

	/**
	 * 成本月度趋势，从1月到当前月
	 */
	@ApiOperation(value = "成本趋势", notes = "返回当年1月至当前月的月度成本趋势数据")
	@GetMapping("/costTrend")
	public Result<java.util.List<org.jeecg.modules.cw.statistics.result.CwCostTrendPointResult>> costTrend(
			@RequestParam(value = "date", required = false)
			@DateTimeFormat(pattern = "yyyy-MM-dd") Date date) {
		java.util.List<org.jeecg.modules.cw.statistics.result.CwCostTrendPointResult> data = cwStatisticsService.costTrend(date);
		return Result.OK(data);
	}

	/**
	 * 按单位获取成本月度趋势，包含实际和计划成本
	 */
	@ApiOperation(value = "按单位成本趋势", notes = "返回指定单位当年1月至当前月的月度成本趋势数据，包含实际和计划成本")
	@GetMapping("/costTrendByUnit")
	public Result<java.util.List<org.jeecg.modules.cw.statistics.result.CwCostTrendPointResult>> costTrendByUnit(
			@RequestParam(value = "date", required = false)
			@DateTimeFormat(pattern = "yyyy-MM-dd") Date date,
			@RequestParam(value = "unit", required = false, defaultValue = "all") String unit) {
		java.util.List<org.jeecg.modules.cw.statistics.result.CwCostTrendPointResult> data = cwStatisticsService.costTrendByUnit(date, unit);
		return Result.OK(data);
	}

	/**
	 * 单位成本柱状图数据
	 */
	@ApiOperation(value = "单位成本柱状图", notes = "返回采矿场、大山厂、泗选厂、精尾厂（月/年）计划值与实际值的总成本及吨矿成本")
	@GetMapping("/costExpandBar")
	public Result<java.util.List<org.jeecg.modules.cw.statistics.result.CwUnitCostBarResult>> costExpandBar(
			@RequestParam(value = "date", required = false)
			@DateTimeFormat(pattern = "yyyy-MM-dd") java.util.Date date,
			@RequestParam(value = "dimension", required = false, defaultValue = "month") String dimension) {
		java.util.List<org.jeecg.modules.cw.statistics.result.CwUnitCostBarResult> data = cwStatisticsService.unitCostBar(date, dimension);
		return Result.OK(data);
	}

	/**
	 * 单位成本详细数据
	 */
	@ApiOperation(value = "单位成本详细", notes = "返回指定单位（月/年）的总/吨成本及成本构成明细")
	@GetMapping("/unitCostDetail")
	public Result<org.jeecg.modules.cw.statistics.result.CwUnitCostDetailResult> unitCostDetail(
			@RequestParam("unit") String unit,
			@RequestParam(value = "date", required = false)
			@DateTimeFormat(pattern = "yyyy-MM-dd") java.util.Date date,
			@RequestParam(value = "dimension", required = false, defaultValue = "month") String dimension) {
		org.jeecg.modules.cw.statistics.result.CwUnitCostDetailResult data = cwStatisticsService.unitCostDetail(unit, date, dimension);
		return Result.OK(data);
	}
}
